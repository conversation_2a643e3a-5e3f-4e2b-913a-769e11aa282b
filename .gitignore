# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
PIPFILE.lock

# Virtual Environment
.venv/
venv/
ENV/
env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/*.log
logs/*.log.*
*.log

# Data files
data/*.csv
data/*.json
data/*.txt
!data/.gitkeep

# Backup files
project_backup/
*.backup

# Temporary files
*.tmp
*.temp
*.bak
*.orig

# System files
.DS_Store
Thumbs.db

# Test files
test_*.log
startup_*.log
cleanup_*.json
cleanup_*.md

# Runtime files
*.pid
*.lock
*.bak
*.old

# Temporary files
*.tmp
*.temp
cleanup_*.md
cleanup_*.json
project_cleanup.py
finalize_project_structure.py

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
~$*.xlsx
