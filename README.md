# 串口通信服务项目

## 项目概述

本项目是一个基于Python的串口通信服务，用于与设备进行数据交换和控制。

## 项目结构

```
SerialPortRule/
├── main.py                 # 主程序入口
├── requirements.txt        # Python依赖包
├── .gitignore             # Git忽略文件
├── config/                # 配置文件目录
│   ├── protocol.json      # 协议配置
│   └── service.conf       # 服务配置
├── src/                   # 源代码目录
│   ├── protocol_parser.py # 协议解析器
│   ├── serial_comm.py     # 串口通信模块
│   ├── config_manager.py  # 配置管理器
│   ├── data_processor.py  # 数据处理器
│   ├── exception_handler.py # 异常处理器
│   ├── log_manager.py     # 日志管理器
│   └── ...               # 其他核心模块
├── docs/                  # 文档目录
├── scripts/               # 脚本目录
├── deployment/            # 部署文件
├── logs/                  # 日志文件
├── data/                  # 数据文件
└── project_backup/        # 备份文件
```

## 核心功能

- **串口通信**: 支持多种串口配置和通信协议
- **数据解析**: 智能解析设备响应数据
- **异常处理**: 完善的错误处理和恢复机制
- **日志记录**: 详细的操作日志和调试信息
- **配置管理**: 灵活的配置文件管理

## 安装和运行

1. 安装依赖:
   ```bash
   pip install -r requirements.txt
   ```

2. 配置串口参数:
   编辑 `config/service.conf` 文件

3. 运行服务:
   ```bash
   python3 main.py
   ```

## 配置说明

- `config/protocol.json`: 协议定义和命令配置
- `config/service.conf`: 服务运行参数配置

## 日志文件

日志文件保存在 `logs/` 目录下，包含详细的运行信息和错误记录。

## 开发说明

本项目采用模块化设计，主要模块包括：

- `protocol_parser.py`: 协议解析和数据处理
- `serial_comm.py`: 串口通信和数据传输
- `config_manager.py`: 配置文件管理
- `exception_handler.py`: 异常处理和错误恢复

## 版本历史

- v1.0: 基础串口通信功能
- v1.1: 优化数据解析和错误处理
- v1.2: 项目结构整理和代码优化

## 许可证

本项目采用MIT许可证。
