[Unit]
Description=Serial Port Communication Service
Documentation=https://github.com/your-repo/SerialPortRule
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/home/<USER>/SerialPortRule
ExecStart=/home/<USER>/SerialPortRule/.venv/bin/python /home/<USER>/SerialPortRule/simple_main.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=serialport-service
Environment=PYTHONPATH=/home/<USER>/SerialPortRule
Environment=PYTHONUNBUFFERED=1

# 安全设置
NoNewPrivileges=false
ProtectSystem=false
ProtectHome=false
ReadWritePaths=/home/<USER>/SerialPortRule/data /home/<USER>/SerialPortRule/logs
PrivateTmp=false

# 资源限制
LimitNOFILE=65536
MemoryMax=512M

[Install]
WantedBy=multi-user.target