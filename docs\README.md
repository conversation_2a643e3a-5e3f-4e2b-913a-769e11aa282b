# Linux Python串口通信后台服务

基于Python开发的Linux系统串口通信后台服务，支持自由协议分析和触摸屏宏文件配置，实现串口数据的请求发送与接收解析。

## 功能特性

- **串口通信**: 支持多种串口参数配置，自动重连机制
- **协议解析**: 基于自由协议分析规则，支持多种数据格式
- **任务调度**: 支持初始化命令和循环命令调度
- **数据处理**: 实时数据处理、计算和存储
- **异常处理**: 完善的异常处理和错误恢复机制
- **状态监控**: 系统资源监控和健康检查
- **systemd集成**: 作为系统守护进程运行
- **日志记录**: 完整的日志记录和轮转功能

## 系统要求

- Linux操作系统 (支持systemd)
- Python 3.8或更高版本
- root权限 (仅安装时需要)

## 快速安装

1. 下载项目文件到本地目录
2. 运行安装脚本:
   ```bash
   sudo chmod +x install.sh
   sudo ./install.sh
   ```

## 手动安装

### 1. 安装依赖

```bash
sudo apt-get update
sudo apt-get install python3 python3-pip
pip3 install pyserial psutil
```

### 2. 创建用户和目录

```bash
sudo useradd -r -s /bin/false serialport
sudo mkdir -p /opt/serialport-service/{config,logs,data,scripts}
```

### 3. 复制文件

```bash
sudo cp -r * /opt/serialport-service/
sudo chown -R serialport:serialport /opt/serialport-service
```

### 4. 安装systemd服务

```bash
sudo cp serialport-service.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable serialport-service
```

## 配置

主配置文件位于: `/opt/serialport-service/config/service.conf`

### 串口配置

```toml
[serial]
port = "/dev/ttyUSB0"
baudrate = 19200
bytesize = 8
parity = "E"
stopbits = 1
timeout = 1.0
write_timeout = 1.0
```

### 协议配置

```toml
[protocol]
frame_start = 0x68
frame_end = 0x16
address = 0x01
```

### 调度配置

```toml
[scheduler]
loop_interval = 1.0
init_commands = ["通信初始化", "参数设置"]
loop_commands = ["DSP", "D0", "D1", "D2"]
```

## 使用方法

### 启动服务

```bash
sudo systemctl start serialport-service
```

### 查看状态

```bash
sudo systemctl status serialport-service
```

### 查看日志

```bash
sudo journalctl -u serialport-service -f
```

### 停止服务

```bash
sudo systemctl stop serialport-service
```

### 重启服务

```bash
sudo systemctl restart serialport-service
```

## 管理脚本

服务提供了便捷的管理脚本，位于 `/opt/serialport-service/scripts/`:

- `start.sh` - 启动服务
- `stop.sh` - 停止服务
- `restart.sh` - 重启服务
- `logs.sh` - 查看实时日志
- `status.sh` - 检查服务状态

## 数据格式

### DSP数据

包含电压、电流、功率等实时数据:

```json
{
  "ab_voltage": 10000,
  "bc_voltage": 10000,
  "ca_voltage": 10000,
  "svg_current_a": 50,
  "load_active_current": 80,
  "timestamp": "2024-01-01 12:00:00"
}
```

### 单元数据 (D0-D8)

包含各相电压、状态、版本信息:

```json
{
  "phase": "A",
  "type": "voltage",
  "voltages": [100, 101, 102],
  "max_voltage": 102,
  "min_voltage": 100
}
```

## 监控和告警

服务内置监控功能，监控以下指标:

- CPU使用率
- 内存使用率
- 磁盘空间
- 网络流量
- 进程状态

告警阈值可在配置文件中设置。

## 故障排除

### 常见问题

1. **串口权限问题**
   ```bash
   sudo usermod -a -G dialout serialport
   ```

2. **服务启动失败**
   ```bash
   sudo journalctl -u serialport-service --no-pager
   ```

3. **Python模块导入错误**
   ```bash
   pip3 install -r requirements.txt
   ```

### 日志位置

- 服务日志: `/opt/serialport-service/logs/service.log`
- 系统日志: `journalctl -u serialport-service`

## 卸载

运行卸载脚本:

```bash
sudo chmod +x uninstall.sh
sudo ./uninstall.sh
```

## 开发

### 项目结构

```
serialport-service/
├── main.py                 # 主程序入口
├── src/                    # 源代码目录
│   ├── config_manager.py   # 配置管理
│   ├── serial_comm.py      # 串口通信
│   ├── protocol_parser.py  # 协议解析
│   ├── data_processor.py   # 数据处理
│   ├── task_scheduler.py   # 任务调度
│   ├── exception_handler.py # 异常处理
│   └── status_monitor.py   # 状态监控
├── config/                 # 配置文件
├── logs/                   # 日志文件
└── data/                   # 数据文件
```

### 扩展开发

1. 添加新的命令处理器
2. 扩展数据处理逻辑
3. 增加新的监控指标
4. 自定义异常处理策略

## 许可证

MIT License

## 支持

如有问题请提交Issue或联系开发团队。