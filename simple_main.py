#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化串口通信服务
严格按照实际运行报文和协议文档实现
"""

import asyncio
import json
import serial
import time
import struct
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional


class SimpleDataRotator:
    """简单的数据文件轮转器"""
    
    def __init__(self, data_dir: str = "data", max_files: int = 3):
        """
        初始化数据轮转器
        
        Args:
            data_dir: 数据目录
            max_files: 最大文件数量
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        self.max_files = max_files
        self.current_file_index = 0
        self.startup_file_created = False
    
    def get_current_file_path(self) -> Path:
        """
        获取当前数据文件路径
        
        Returns:
            当前文件路径
        """
        return self.data_dir / f"data_{self.current_file_index}.json"
    
    def rotate_file(self):
        """轮转到下一个文件"""
        self.current_file_index = (self.current_file_index + 1) % self.max_files
        
        # 清空新的当前文件
        current_file = self.get_current_file_path()
        if current_file.exists():
            current_file.unlink()
    
    def save_startup_data(self, data: Dict[str, Any]):
        """
        保存启动序列数据到专门的文件
        
        Args:
            data: 要保存的启动序列数据
        """
        startup_file = self.data_dir / "startup_sequence.json"
        
        # 如果是第一次保存启动数据，创建新文件
        if not self.startup_file_created:
            with open(startup_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False)
                f.write('\n')
            self.startup_file_created = True
        else:
            # 追加数据到启动文件
            with open(startup_file, 'a', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False)
                f.write('\n')
    
    def save_data(self, data: Dict[str, Any]):
        """
        保存数据到当前文件
        
        Args:
            data: 要保存的数据
        """
        current_file = self.get_current_file_path()
        
        # 检查文件大小，如果超过50KB则轮转
        if current_file.exists() and current_file.stat().st_size > 50 * 1024:
            self.rotate_file()
            current_file = self.get_current_file_path()
        
        # 追加数据到文件
        with open(current_file, 'a', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False)
            f.write('\n')


class RealProtocolParser:
    """真实协议解析器，严格按照实际报文格式解析"""
    
    def __init__(self):
        """初始化协议解析器"""
        # 根据实际报文定义命令格式
        self.commands = {
            'C0': {'name': 'IO状态查询', 'response_data_length': 8},
            'C1': {'name': '故障报警查询', 'response_data_length': 68},
            'C3': {'name': '故障录波查询', 'response_data_length': 200},
            'C4': {'name': 'DSP状态查询', 'response_data_length': 192},
            'C5': {'name': '版本信息查询', 'response_data_length': 48},
            '59': {'name': '调试参数查询', 'response_data_length': 4, 'multi_frame': True},
            '57': {'name': '调试参数修改', 'response_data_length': 0},
            '55': {'name': '修改返回确认', 'response_data_length': 8},
            '37': {'name': '修改完成确认', 'response_data_length': 0},
            '5B': {'name': '修改完成确认返回', 'response_data_length': 8},
            'D0': {'name': 'A相单元直流侧电压', 'response_data_length': 36},
            'D1': {'name': 'B相单元直流侧电压', 'response_data_length': 36},
            'D2': {'name': 'C相单元直流侧电压', 'response_data_length': 36},
            'D3': {'name': 'A相单元状态', 'response_data_length': 36},
            'D4': {'name': 'B相单元状态', 'response_data_length': 36},
            'D5': {'name': 'C相单元状态', 'response_data_length': 36},
            'D6': {'name': 'A相单元程序版本信息', 'response_data_length': 36},
            'D7': {'name': 'B相单元程序版本信息', 'response_data_length': 36},
            'D8': {'name': 'C相单元程序版本信息', 'response_data_length': 36}
        }
    
    def calculate_checksum(self, data: bytes) -> int:
        """
        计算单字节和校验
        
        Args:
            data: 待校验的数据
            
        Returns:
            校验值
        """
        return sum(data) % 256
    
    def build_command(self, command: str, data_bytes: bytes = b'', channel_info: bytes = b'') -> bytes:
        """
        构建命令帧
        
        Args:
            command: 命令代码
            data_bytes: 数据字节
            channel_info: 通道信息（用于C3命令）
            
        Returns:
            完整的命令帧
        """
        # 帧头
        frame = bytearray([0xEB, 0x90, 0x01, 0x01])
        
        # 命令
        frame.append(int(command, 16))
        
        # 根据命令类型构建不同格式
        if command == 'C3':  # 故障录波特殊格式
            frame.append(0xC8)  # 数据字节数
            frame.append(0x00)  # 空字节
            frame.extend(channel_info if channel_info else b'\x00\x00')  # 通道信息
            frame.extend(b'\x00' * 5)  # 填充到13字节
        elif command == '57':  # 调试参数修改特殊格式
            frame.append(0x04)  # 数据字节数
            frame.append(0x00)  # 空字节
            frame.extend(data_bytes[:6] if len(data_bytes) >= 6 else data_bytes + b'\x00' * (6 - len(data_bytes)))
        else:  # 其他命令标准格式
            frame.extend(b'\x00' * 8)  # 8个空字节
        
        # 计算校验
        checksum = self.calculate_checksum(frame)
        frame.append(checksum)
        
        # 帧尾
        frame.extend([0xAA, 0xAB])
        
        return bytes(frame)
    
    def parse_response(self, command: str, raw_data: bytes) -> Dict[str, Any]:
        """
        解析命令响应数据
        
        Args:
            command: 命令代码
            raw_data: 原始响应数据
            
        Returns:
            解析结果
        """
        result = {
            'command': command,
            'timestamp': time.time(),
            'raw_hex': raw_data.hex().upper(),
            'success': False,
            'data': None,
            'parsed_data': None
        }
        
        if len(raw_data) < 7:  # 最小帧长度检查
            result['error'] = f'响应帧太短: {len(raw_data)} 字节'
            return result
        
        # 检查帧头和帧尾
        if raw_data[:4] != b'\xEB\x90\x01\x01' or raw_data[-2:] != b'\xAA\xAB':
            result['error'] = '帧头或帧尾不正确'
            return result
        
        # 检查命令码
        response_cmd = f'{raw_data[4]:02X}'
        if response_cmd != command:
            result['error'] = f'响应命令不匹配，期望: {command}, 实际: {response_cmd}'
            return result
        
        # 提取数据部分
        if command == '59':  # 59命令特殊处理
            if len(raw_data) >= 13:
                data_length = raw_data[5]
                if data_length == 0x04:
                    # 提取序号和数据
                    seq_bytes = raw_data[7:9]
                    data_bytes = raw_data[9:13]
                    
                    sequence = struct.unpack('<H', seq_bytes)[0]  # 小端序号
                    float_value = struct.unpack('<f', data_bytes)[0]  # 小端浮点数
                    
                    result['data_hex'] = raw_data[7:13].hex().upper()
                    result['data_bytes'] = list(raw_data[7:13])
                    result['parsed_data'] = {
                        'sequence': sequence,
                        'value': float_value
                    }
        else:
            # 其他命令标准处理
            if len(raw_data) >= 8:
                data_length = raw_data[5]
                if data_length > 0 and len(raw_data) >= 8 + data_length:
                    data_start = 8
                    data_end = data_start + data_length
                    data_bytes = raw_data[data_start:data_end]
                    
                    result['data_hex'] = data_bytes.hex().upper()
                    result['data_bytes'] = list(data_bytes)
                    
                    # 根据命令类型解析数据
                    if command in ['C4', 'C3']:  # 浮点数数据
                        result['parsed_data'] = self._parse_float_array(data_bytes)
                    elif command in ['D0', 'D1', 'D2', 'D3', 'D4', 'D5', 'D6', 'D7', 'D8']:  # 16位整数数据
                        result['parsed_data'] = self._parse_uint16_array(data_bytes)
                    elif command == 'C1':  # 故障报警16位数据
                        result['parsed_data'] = self._parse_uint16_array(data_bytes)
                    elif command == 'C0':  # IO状态32位数据
                        result['parsed_data'] = self._parse_uint32_array(data_bytes)
                    else:
                        result['parsed_data'] = data_bytes.hex().upper()
        
        result['success'] = True
        return result
    
    def _parse_float_array(self, data: bytes) -> List[float]:
        """解析浮点数组（小端格式）"""
        result = []
        for i in range(0, len(data), 4):
            if i + 4 <= len(data):
                float_bytes = data[i:i+4]
                float_value = struct.unpack('<f', float_bytes)[0]
                result.append(float_value)
        return result
    
    def _parse_uint16_array(self, data: bytes) -> List[int]:
        """解析16位无符号整数数组（小端格式）"""
        result = []
        for i in range(0, len(data), 2):
            if i + 2 <= len(data):
                uint16_bytes = data[i:i+2]
                uint16_value = struct.unpack('<H', uint16_bytes)[0]
                result.append(uint16_value)
        return result
    
    def _parse_uint32_array(self, data: bytes) -> List[int]:
        """解析32位无符号整数数组（小端格式）"""
        result = []
        for i in range(0, len(data), 4):
            if i + 4 <= len(data):
                uint32_bytes = data[i:i+4]
                uint32_value = struct.unpack('<I', uint32_bytes)[0]
                result.append(uint32_value)
        return result


class RealSerialService:
    """真实串口通信服务"""
    
    def __init__(self):
        """初始化串口服务"""
        self.serial_port = None
        self.parser = RealProtocolParser()
        self.data_rotator = SimpleDataRotator()
        self.running = False
        
        # 命令序列定义
        self.startup_sequence = ['C0', '59', 'C5', 'D6', 'D7', 'D8']
        self.loop_sequence = ['C1', 'C4', 'D0', 'D1', 'D2', 'C4', 'D3', 'D4', 'D5', 'C4', 'C0']
        
        self.startup_completed = False
        self.in_startup_sequence = False
        self.current_loop_index = 0
        self.param_59_frames_received = 0
        self.param_59_total_frames = 259  # 根据实际运行报文，59命令会接收大量帧数据
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open('config/service.conf', 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 简单解析TOML格式
            config = {}
            for line in content.split('\n'):
                line = line.strip()
                if '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"')
                    config[key] = value
            
            return {
                'port': config.get('port', '/dev/ttyUSB0'),
                'baudrate': int(config.get('baudrate', '19200')),
                'timeout': float(config.get('timeout', '2.0'))
            }
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            return {
                'port': '/dev/ttyUSB0',
                'baudrate': 19200,
                'timeout': 2.0
            }
    
    async def start(self):
        """启动串口服务"""
        print("启动真实串口通信服务...")
        
        config = self.load_config()
        
        try:
            # 尝试打开串口
            self.serial_port = serial.Serial(
                port=config['port'],
                baudrate=config['baudrate'],
                bytesize=8,
                parity='O',  # 奇校验
                stopbits=1,
                timeout=config['timeout']
            )
            print(f"串口 {config['port']} 打开成功")
            
        except Exception as e:
            print(f"串口打开失败: {e}")
            return
        
        self.running = True
        
        try:
            # 执行启动序列
            await self._execute_startup_sequence()
            
            # 执行循环序列
            while self.running:
                await self._execute_loop_sequence()
                await asyncio.sleep(0.1)  # 短暂延时
                
        except Exception as e:
            print(f"通信过程中发生错误: {e}")
        finally:
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
                print("串口已关闭")
    
    async def _execute_startup_sequence(self):
        """执行启动序列"""
        print("执行启动序列...")
        self.in_startup_sequence = True
        
        for i, command in enumerate(self.startup_sequence):
            if not self.running:
                break
            
            print(f"执行启动序列命令 {i+1}/{len(self.startup_sequence)}: {command}")
            await self._execute_command(command)
            print(f"启动序列命令 {command} 执行完成")
            await asyncio.sleep(0.2)  # 命令间延时
        
        self.startup_completed = True
        self.in_startup_sequence = False
        print("启动序列完成")
    
    async def _execute_loop_sequence(self):
        """执行循环序列"""
        if not self.startup_completed:
            return
        
        command = self.loop_sequence[self.current_loop_index]
        await self._execute_command(command)
        
        self.current_loop_index = (self.current_loop_index + 1) % len(self.loop_sequence)
        await asyncio.sleep(0.1)  # 循环命令间延时
    
    async def _execute_command(self, command: str):
        """执行单个命令"""
        if not self.serial_port or not self.serial_port.is_open:
            return
        
        try:
            # 构建命令帧
            cmd_frame = self.parser.build_command(command)
            
            # 发送命令
            self.serial_port.write(cmd_frame)
            print(f"发送命令 {command}: {cmd_frame.hex().upper()}")
            
            # 接收响应
            if command == '59':  # 59命令需要接收多帧
                await self._receive_59_responses(command)
            else:
                response = self._receive_response()
                if response:
                    await self._process_response(command, response)
                    
        except Exception as e:
            print(f"命令 {command} 执行失败: {e}")
    
    async def _receive_59_responses(self, command: str):
        """接收59命令的多帧响应"""
        frames_received = 0
        
        while frames_received < self.param_59_total_frames and self.running:
            response = self._receive_response()
            if response:
                await self._process_response(command, response)
                frames_received += 1
            else:
                break  # 超时或无响应
            
            await asyncio.sleep(0.01)  # 帧间短暂延时
    
    def _receive_response(self) -> Optional[bytes]:
        """接收响应数据"""
        try:
            # 读取帧头
            header = self.serial_port.read(4)
            if len(header) != 4 or header != b'\xEB\x90\x01\x01':
                return None
            
            # 读取命令和数据长度
            cmd_and_length = self.serial_port.read(2)
            if len(cmd_and_length) != 2:
                return None
            
            data_length = cmd_and_length[1]
            
            # 读取剩余数据
            remaining_length = 3 + data_length + 1 + 2  # 3空字节 + 数据 + 校验 + 帧尾
            remaining_data = self.serial_port.read(remaining_length)
            
            if len(remaining_data) != remaining_length:
                return None
            
            # 组合完整响应
            full_response = header + cmd_and_length + remaining_data
            return full_response
            
        except Exception as e:
            print(f"接收响应失败: {e}")
            return None
    
    async def _process_response(self, command: str, response: bytes):
        """处理响应数据"""
        print(f"接收响应 {command}: {response.hex().upper()}")
        
        # 解析响应
        parsed = self.parser.parse_response(command, response)
        
        if parsed['success']:
            print(f"命令 {command} 执行成功")
        else:
            print(f"命令 {command} 解析失败: {parsed.get('error', '未知错误')}")
        
        # 根据启动序列状态选择保存方法
        if self.in_startup_sequence:
            self.data_rotator.save_startup_data(parsed)
        else:
            self.data_rotator.save_data(parsed)
    
    async def stop(self):
        """停止服务"""
        print("正在停止串口服务...")
        self.running = False
        
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
        
        print("串口服务已停止")


async def main():
    """主函数"""
    service = RealSerialService()
    
    try:
        await service.start()
    except KeyboardInterrupt:
        print("\n收到中断信号")
    finally:
        await service.stop()


if __name__ == "__main__":
    asyncio.run(main())